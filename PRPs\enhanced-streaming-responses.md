name: "Enhanced Streaming Responses for Telegram Bot"
description: |

## Purpose
Implement advanced streaming responses for the Telegram bot using Agent Hustle API's structured chunk processing to provide real-time tool execution visibility and optimized message updates.

## Core Principles
1. **Context is King**: Leverage existing patterns from app.js for error handling and logging
2. **Validation Loops**: Test with different chunk types and error scenarios
3. **Information Dense**: Use structured chunk processing for better UX
4. **Progressive Success**: Start with basic improvements, then enhance
5. **Global rules**: Follow existing codebase patterns and security practices

---

## Goal
Enhance the existing `/hustlestream` command in app.js to provide better real-time streaming responses with tool call visualization, smart message batching, and improved error handling using the Agent Hustle API's advanced streaming capabilities.

## Why
- **User Experience**: Users currently see only basic "thinking" indicators without knowing what tools are being used
- **Performance**: Current implementation updates messages every 1 second regardless of content changes
- **Transparency**: Tool execution is logged but not visible to users
- **Error Recovery**: Basic error handling doesn't provide informative feedback about stream issues

## What
Transform the current basic streaming implementation into an advanced system that:
- Shows real-time tool execution progress
- Batches text updates intelligently (500ms accumulation)
- Provides immediate updates for tool calls/results
- Handles stream interruptions gracefully
- Respects Telegram's rate limits

### Success Criteria
- [ ] Users see when AI is using specific tools (trading, price checks, etc.)
- [ ] Message edit frequency reduced by 50% while maintaining responsiveness
- [ ] Tool execution progress visible in real-time
- [ ] Graceful handling of stream timeouts and errors
- [ ] No rate limit violations during normal operation

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- url: https://github.com/EmblemCompany/hustle-incognito
  why: Official API documentation for chatStream with processChunks option
  
- file: app.js (lines 324-485)
  why: Current streaming implementation patterns to enhance
  
- url: https://gramio.dev/rate-limits
  section: Message editing rate limits and best practices
  critical: Telegram allows ~30 message edits per minute per chat
  
- file: app.js (lines 1-50)
  why: CONFIG object patterns and existing timeout configurations
  
- file: app.js (lines 52-88)
  why: secureLog function pattern for consistent logging
```

### Current Codebase tree (relevant files)
```bash
app.js                          # Main bot implementation with /hustlestream
.env                           # Environment configuration
package.json                   # Dependencies including hustle-incognito
AgentHustleGiude.md           # Integration examples and patterns
```

### Desired Codebase tree with files to be added and responsibility of file
```bash
app.js                          # Enhanced /hustlestream command (MODIFY)
# No new files needed - enhancement of existing functionality
```

### Known Gotchas of our codebase & Library Quirks
```javascript
// CRITICAL: hustle-incognito requires processChunks: true for structured chunks
// Example: stream = hustleClient.chatStream({ processChunks: true })

// CRITICAL: Telegram rate limits - max ~30 message edits per minute per chat
// Current implementation: updates every 1000ms regardless of content

// CRITICAL: CONFIG object is global and contains all timeout configurations
// Pattern: CONFIG.STREAM_TIMEOUT, CONFIG.MAX_MESSAGE_SIZE, etc.

// CRITICAL: secureLog function is used throughout for consistent logging
// Pattern: secureLog('info', 'message'), secureLog('error', 'error message')

// CRITICAL: Stream events use .on() pattern, not async iteration
// Current: stream.on('data', callback), stream.on('end', callback)
```

## Implementation Blueprint

### Data models and structure
```javascript
// Chunk processing state management
const streamState = {
  fullResponse: '',
  activeTools: new Set(),
  lastTextUpdate: 0,
  pendingTextChunks: '',
  isStreaming: true,
  toolCallsInProgress: new Map()
};

// Status message templates
const STATUS_MESSAGES = {
  thinking: '🤖 Hustle is thinking...',
  toolCall: (toolName) => `🔧 Using ${toolName} tool...`,
  toolResult: (toolName) => `📊 ${toolName} completed`,
  processing: '⚡ Processing results...',
  complete: '✅ Complete!'
};
```

### list of tasks to be completed to fullfill the PRP in the order they should be completed

```yaml
Task 1:
MODIFY app.js (lines 324-485):
  - FIND pattern: "case '/hustlestream':"
  - REPLACE current streaming implementation with enhanced version
  - PRESERVE existing error handling patterns and secureLog usage
  - KEEP existing validation and initial message sending

Task 2:
ADD enhanced chunk processing functions to app.js:
  - CREATE processTextChunk() function for batched text updates
  - CREATE processToolCall() function for immediate tool visualization
  - CREATE processToolResult() function for tool completion updates
  - MIRROR existing updateMessageWithIndicator pattern

Task 3:
IMPLEMENT smart message batching system:
  - ADD 500ms text accumulation timer
  - PRESERVE immediate updates for tool events
  - KEEP existing rate limiting respect patterns

Task 4:
ENHANCE error handling and recovery:
  - EXTEND existing stream error handling
  - ADD specific handling for chunk processing errors
  - MAINTAIN existing timeout and size limit patterns
```

### Per task pseudocode as needed added to each task

```javascript
// Task 1: Enhanced /hustlestream case structure
case '/hustlestream':
  // PATTERN: Keep existing validation (lines 326-336)
  const streamMessage = args.trim();
  if (!streamMessage) { /* existing validation */ }

  try {
    // PATTERN: Keep existing initial message (lines 341-343)
    const initialMessage = await bot.sendMessage(chatId, STATUS_MESSAGES.thinking);
    const messageId = initialMessage.message_id;

    // ENHANCED: Use structured chunk processing
    const stream = hustleClient.chatStream({
      vaultId: `telegram-${chatId}`,
      messages: [{ role: 'user', content: streamMessage }],
      processChunks: true  // CRITICAL: Enable structured chunks
    });

    // ENHANCED: Initialize streaming state
    const streamState = initializeStreamState();

    // ENHANCED: Process chunks by type
    stream.on('data', (chunk) => processChunkByType(chunk, streamState, bot, chatId, messageId));

    // PATTERN: Keep existing end/error handlers with enhancements
  } catch (error) { /* existing error pattern */ }

// Task 2: Chunk processing functions
async function processChunkByType(chunk, streamState, bot, chatId, messageId) {
  // PATTERN: Use existing streamSize monitoring
  streamState.streamSize += chunk.toString().length;
  if (streamState.streamSize > CONFIG.MAX_RESPONSE_SIZE) {
    // PATTERN: Use existing size limit handling
  }

  switch (chunk.type) {
    case 'text':
      await processTextChunk(chunk, streamState, bot, chatId, messageId);
      break;
    case 'tool_call':
      await processToolCall(chunk, streamState, bot, chatId, messageId);
      break;
    case 'tool_result':
      await processToolResult(chunk, streamState, bot, chatId, messageId);
      break;
    case 'finish':
      await processFinish(chunk, streamState, bot, chatId, messageId);
      break;
  }
}

// Task 3: Smart batching for text chunks
async function processTextChunk(chunk, streamState, bot, chatId, messageId) {
  // ENHANCED: Accumulate text chunks
  streamState.pendingTextChunks += chunk.value;

  // ENHANCED: Batch updates every 500ms
  const now = Date.now();
  if (now - streamState.lastTextUpdate >= 500) {
    streamState.fullResponse += streamState.pendingTextChunks;
    streamState.pendingTextChunks = '';
    streamState.lastTextUpdate = now;

    // PATTERN: Use existing editMessageText pattern
    await updateMessage(bot, chatId, messageId, streamState.fullResponse);
  }
}

// Task 4: Tool call visualization
async function processToolCall(chunk, streamState, bot, chatId, messageId) {
  // ENHANCED: Track active tools
  const toolName = chunk.value.name;
  streamState.activeTools.add(toolName);
  streamState.toolCallsInProgress.set(chunk.value.id, toolName);

  // ENHANCED: Immediate update for tool calls
  const statusMessage = STATUS_MESSAGES.toolCall(toolName);
  await updateMessage(bot, chatId, messageId, streamState.fullResponse + '\n\n' + statusMessage);

  // PATTERN: Use existing secureLog pattern
  secureLog('info', `Agent using tool: ${toolName}`);
}
```

### Integration Points
```yaml
CONFIG:
  - add to: app.js CONFIG object (lines 4-9)
  - pattern: "TEXT_BATCH_INTERVAL: 500, // 500ms for text batching"

LOGGING:
  - use existing: secureLog function pattern throughout
  - pattern: "secureLog('info', 'Enhanced streaming: tool call received')"

ERROR_HANDLING:
  - extend existing: stream error handlers (lines 419-429)
  - pattern: "Keep existing try/catch structure, add chunk-specific errors"
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
node --check app.js              # Syntax validation
# Expected: No syntax errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests each new feature/function use existing test patterns
```javascript
// CREATE test cases for new functionality:
// Test 1: Text chunk batching
async function testTextChunkBatching() {
  // Simulate rapid text chunks
  // Verify only one message update per 500ms
}

// Test 2: Tool call immediate updates
async function testToolCallUpdates() {
  // Simulate tool_call chunk
  // Verify immediate message update with tool name
}

// Test 3: Error handling
async function testStreamErrors() {
  // Simulate stream errors
  // Verify existing error patterns still work
}
```

```bash
# Manual testing approach (no automated tests in current codebase):
# 1. Start bot: node app.js
# 2. Test /hustlestream with simple text response
# 3. Test /hustlestream with tool-using query (e.g., "What's the price of SOL?")
# 4. Test error conditions (invalid API key, network issues)
```

### Level 3: Integration Test
```bash
# Start the bot
node app.js

# Test enhanced streaming
# Send: /hustlestream What's the current price of Bitcoin and Ethereum?
# Expected: See tool execution progress, batched text updates

# Test error handling
# Disconnect network during stream
# Expected: Graceful error message, no bot crash
```

## Final validation Checklist
- [ ] Bot starts without errors: `node app.js`
- [ ] Text chunks are batched (max 2 updates per second)
- [ ] Tool calls show immediately with tool names
- [ ] Stream errors handled gracefully
- [ ] No rate limit violations during testing
- [ ] Existing functionality preserved (/hustle, /start, etc.)
- [ ] Logs are informative using existing secureLog pattern

---

## Anti-Patterns to Avoid
- ❌ Don't create new logging patterns when secureLog exists
- ❌ Don't skip existing validation patterns
- ❌ Don't ignore Telegram rate limits (max 30 edits/minute)
- ❌ Don't use sync functions in async context
- ❌ Don't hardcode timeouts when CONFIG object exists
- ❌ Don't catch all exceptions - use existing specific error patterns

## Confidence Score: 9/10
High confidence due to:
- Clear existing patterns to follow in app.js
- Well-documented hustle-incognito API
- Incremental enhancement approach
- Existing error handling to build upon
- Specific validation steps provided
