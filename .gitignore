# Dependencies
node_modules/
npm-debug.log
yarn-debug.log*
yarn-error.log*
package-lock.json

# Environment variables
.env
.env.local
.env.*.local

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# Debug logs
logs/
*.log
npm-debug.log*

# Build output
dist/
build/
out/

# Project specific
.cursor/
.vibesync/

# Testing
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
.cache/ 