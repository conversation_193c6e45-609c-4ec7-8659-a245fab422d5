# Feature: Enhanced Streaming Responses for Telegram Bot

## Goal  
Improve the existing `/hustlestream` command implementation to provide better real-time streaming responses using the Agent Hustle API's advanced streaming capabilities. The current implementation has basic streaming but can be enhanced to handle tool calls, better error handling, and more responsive user experience.

## Stack Context  
- Framework: Node.js with ES modules
- Languages: JavaScript  
- Bot API: node-telegram-bot-api
- AI API: hustle-incognito (Agent Hustle API)
- Environment: dotenv for configuration

## Current Implementation Analysis
The existing `/hustlestream` command in `app.js` (lines 324-485) has:
- Basic streaming with `hustleClient.chatStream()`
- Message editing every 1 second
- Simple "thinking" indicator
- Basic error handling
- Stream size monitoring

## Requirements  

### Enhanced Streaming Features
1. **Improved Chunk Processing**: Use the `processChunks: true` option to get structured chunk types
2. **Tool Call Visualization**: Show when AI is using tools (trading, price checks, etc.)
3. **Better Progress Indicators**: Different indicators for text generation vs tool execution
4. **Optimized Message Updates**: Smart batching to reduce API calls while maintaining responsiveness
5. **Enhanced Error Recovery**: Better handling of stream interruptions and timeouts

### Streaming Chunk Types to Handle
Based on hustle-incognito documentation:
- `text`: Regular AI response text
- `tool_call`: When AI invokes a tool (show tool name and purpose)
- `tool_result`: When tool execution completes
- `finish`: Stream completion with reason

### User Experience Improvements
1. **Dynamic Status Messages**: 
   - "🤖 Hustle is thinking..."
   - "🔧 Using [tool_name] tool..."
   - "📊 Processing results..."
   - "✅ Complete!"

2. **Smart Message Batching**: 
   - Accumulate text chunks for 500ms before updating
   - Immediate updates for tool calls/results
   - Final message cleanup

3. **Progress Visualization**:
   - Show active tools being used
   - Indicate when multiple tools are running
   - Display completion status

## External Libraries  
Current dependencies are sufficient:
- [node-telegram-bot-api](https://github.com/yagop/node-telegram-bot-api) – Telegram Bot API
- [hustle-incognito](https://github.com/EmblemCompany/hustle-incognito) – Agent Hustle API client

## Implementation Details

### Enhanced `/hustlestream` Command Structure
```javascript
case '/hustlestream':
  // 1. Validate input and send initial message
  // 2. Initialize streaming with processChunks: true
  // 3. Handle different chunk types with appropriate UI updates
  // 4. Implement smart batching for text chunks
  // 5. Show tool execution progress
  // 6. Handle completion and cleanup
```

### Chunk Processing Logic
```javascript
for await (const chunk of stream) {
  switch (chunk.type) {
    case 'text':
      // Batch text updates
    case 'tool_call':
      // Show tool execution immediately
    case 'tool_result':
      // Update with tool results
    case 'finish':
      // Final cleanup and completion
  }
}
```

### Message Update Strategy
- **Text Chunks**: Batch for 500ms, then update message
- **Tool Events**: Immediate updates to show progress
- **Error Handling**: Graceful degradation with user-friendly messages
- **Rate Limiting**: Respect Telegram's message edit limits

## Output Expectations  

### Files to Modify
- `app.js`: Enhance the existing `/hustlestream` command handler (lines 324-485)

### Code Structure
1. **Enhanced Streaming Handler**: Replace current basic implementation
2. **Chunk Type Processors**: Separate functions for each chunk type
3. **Message Batching System**: Smart accumulation and update logic
4. **Tool Visualization**: User-friendly tool execution display
5. **Error Recovery**: Robust error handling and user feedback

### Specific Improvements
1. **Better Status Indicators**: Dynamic messages based on current activity
2. **Tool Call Transparency**: Show users what tools are being used
3. **Optimized Performance**: Reduce unnecessary message edits
4. **Enhanced Error Messages**: More informative error feedback
5. **Stream Monitoring**: Better tracking of stream health and progress

### Configuration Options
- Configurable update intervals (default: 500ms for text, immediate for tools)
- Maximum message length handling
- Timeout configurations for different stream types
- Debug logging for stream events

## Success Criteria
1. Users see real-time progress of AI thinking and tool usage
2. Reduced message edit frequency while maintaining responsiveness  
3. Clear indication when tools are being used and their results
4. Graceful handling of stream interruptions
5. Better overall user experience with informative status updates

## Testing Approach
1. Test with simple text-only responses
2. Test with single tool usage (price checks, token info)
3. Test with multiple tool usage scenarios
4. Test error conditions (network issues, API timeouts)
5. Test message length limits and truncation
6. Performance testing with concurrent users
