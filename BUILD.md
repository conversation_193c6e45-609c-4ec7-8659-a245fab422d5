# Building the Agent Hustle Telegram Bot

This guide will walk you through setting up and building the Agent Hustle Telegram bot from scratch.

## Prerequisites

- Node.js (v14 or higher)
- npm (Node Package Manager)
- A Telegram account
- An Agent Hustle API key
- Basic knowledge of JavaScript/Node.js

## Step 1: Initial Setup

1. Create a new directory for your project:
   ```bash
   mkdir hutlebot
   cd hutlebot
   ```

2. Initialize a new Node.js project:
   ```bash
   npm init -y
   ```

3. Install required dependencies:
   ```bash
   npm install node-telegram-bot-api hustle-incognito dotenv
   ```

## Step 2: Bot Configuration

1. Create a new bot on Telegram:
   - Open Telegram and search for "@BotFather"
   - Send `/newbot` command
   - Follow the prompts to create your bot
   - Save the bot token provided by BotFather

2. Create a `.env` file in your project root:
   ```env
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
   HUSTLE_API_KEY=your_hustle_api_key_here
   ```

## Step 3: Project Structure

1. Create the main application file:
   ```bash
   touch app.js
   ```

2. Set up the project structure:
   ```
   hutlebot/
   ├── .env
   ├── app.js
   ├── package.json
   └── package-lock.json
   ```

## Step 4: Implementation

1. Update `package.json` to use ES modules:
   Add this line:
   ```json
   {
     "type": "module"
   }
   ```

2. Implement the bot functionality in `app.js`:
   - Environment variable handling
   - Telegram bot initialization
   - Command handlers (/start, /help, /hustle)
   - Message deduplication
   - Error handling
   - Agent Hustle integration

## Step 5: Available Commands

The bot supports the following commands:
- `/start` - Shows welcome message and available commands
- `/help` - Displays help information
- `/hustle [message]` - Sends a message to Agent Hustle
- `/hustlestream [message]` - (Coming soon) Streams responses from Agent Hustle

## Step 6: Running the Bot

1. Start the bot:
   ```bash
   npm start
   ```

2. The bot will show connection status and available commands in the console.

## Error Handling

The bot includes comprehensive error handling for:
- Missing environment variables
- Telegram API errors
- Agent Hustle API errors
- Network connectivity issues
- Invalid commands

## Security Features

1. Environment Variables:
   - Sensitive data stored in `.env` file
   - File ignored by git

2. Message Deduplication:
   - Prevents duplicate responses
   - 5-second cache timeout
   - Unique message ID tracking

3. Command Validation:
   - Proper command format checking
   - Bot username verification
   - Empty message handling

## Deployment Considerations

1. Environment:
   - Node.js runtime
   - Stable internet connection
   - Proper environment variables

2. Monitoring:
   - Console logging enabled
   - Error tracking
   - Command processing status

3. Maintenance:
   - Regular dependency updates
   - API key rotation
   - Log monitoring

## Troubleshooting

Common issues and solutions:

1. Bot not responding:
   - Check environment variables
   - Verify internet connection
   - Confirm bot token validity

2. Agent Hustle errors:
   - Verify API key
   - Check request format
   - Monitor rate limits

3. Duplicate messages:
   - Message deduplication system handles this
   - Check MESSAGE_CACHE_TIMEOUT setting

## Development Notes

1. Code Structure:
   - ES Module format
   - Async/await patterns
   - Central command handler
   - Modular design

2. Best Practices:
   - Comprehensive error handling
   - Detailed logging
   - Clean command processing
   - Message deduplication

## Future Enhancements

Planned features:
1. Streaming responses
2. User session management
3. Enhanced error reporting
4. Command rate limiting
5. Admin commands

## Support

For issues or questions:
1. Check the error logs
2. Verify configuration
3. Test in development environment
4. Contact bot administrator 